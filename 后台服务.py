import json
import os
import time
import threading
import logging
from flask import Flask, request, jsonify
from configparser import ConfigParser
from mijiaAPI import mijiaAPI

# --- 基础设置 ---
# 配置日志记录器，使其输出中文信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 加载配置文件
config = ConfigParser()
config.read('config.ini')
SERVER_HOST = config.get('server', 'host')
SERVER_PORT = config.getint('server', 'port')
AUTH_CHECK_INTERVAL = config.getint('mijia', 'check_interval_seconds')
AUTH_JSON_PATH = 'jsons/auth.json'

# --- 全局状态变量 ---
app = Flask(__name__)
api_client = None
auth_status = "initializing"  # 状态可以是: initializing (初始化中), ok (正常), expired (已过期)
last_auth_check_time = None
auth_file_last_modified = 0
name_to_id_cache = {}  # 缓存结构: {'家庭名称': {'场景名称': '场景ID'}}
# 使用线程锁确保多线程访问共享资源时的安全
lock = threading.Lock()

def refresh_cache_logic():
    """
    获取所有家庭和场景信息，以构建名称到ID的映射缓存。
    此函数应在获取锁之后调用。
    成功返回 True, 失败返回 False。
    """
    global name_to_id_cache, api_client
    if not api_client:
        return False
    
    logging.info("正在尝试刷新缓存...")
    try:
        new_cache = {}
        homes = api_client.get_homes_list()
        if not homes:
            logging.warning("无法获取家庭列表，或未找到任何家庭。")
            return False
            
        for home in homes:
            home_id = home.get('id')
            home_name = home.get('name')
            if not home_id or not home_name:
                continue

            new_cache[home_name] = {}
            scenes = api_client.get_scenes_list(home_id)
            if scenes:
                for scene in scenes:
                    new_cache[home_name][scene.get('name')] = scene.get('scene_id')
        
        name_to_id_cache = new_cache
        logging.info(f"缓存刷新成功。共找到 {len(homes)} 个家庭。")
        return True
    except Exception as e:
        logging.error(f"刷新缓存失败: {e}")
        # 刷新缓存失败，可能是因为认证信息过期，后台轮询线程会处理状态变更
        return False

def initialize_api_and_cache():
    """
    初始化 mijiaAPI 客户端并执行首次缓存构建。
    这是服务启动或从故障中恢复的核心函数。
    成功返回 True, 失败返回 False。
    """
    global api_client, auth_status, auth_file_last_modified
    
    with lock:
        logging.info("正在尝试初始化 API 客户端并构建缓存...")
        if not os.path.exists(AUTH_JSON_PATH):
            logging.error(f"文件 {AUTH_JSON_PATH} 未找到。请先运行 '登录.py' 文件。")
            auth_status = "expired"
            return False
        
        try:
            with open(AUTH_JSON_PATH, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)
            
            # 初始化客户端
            api_client = mijiaAPI(auth_data)
            
            # 执行初次缓存构建，如果失败，则整个初始化失败
            if refresh_cache_logic():
                auth_status = "ok"
                auth_file_last_modified = os.path.getmtime(AUTH_JSON_PATH)
                logging.info("API 客户端初始化成功，缓存已成功构建。")
                return True
            else:
                # 如果缓存构建失败，很可能是认证问题
                auth_status = "expired"
                logging.error("在初始化过程中构建缓存失败。将认证状态设置为 '已过期'。")
                return False

        except Exception as e:
            logging.error(f"初始化失败: {e}")
            auth_status = "expired"
            api_client = None
            return False

def auth_polling_thread():
    """
    后台轮询线程，主动检查认证状态并处理自动恢复逻辑。
    """
    global auth_status, last_auth_check_time, api_client
    
    while True:
        time.sleep(AUTH_CHECK_INTERVAL)
        logging.info("后台线程正在执行认证状态检查...")
        
        with lock:
            if auth_status == "expired":
                try:
                    # 检查用户是否已更新 auth.json 文件
                    current_mtime = os.path.getmtime(AUTH_JSON_PATH)
                    if current_mtime > auth_file_last_modified:
                        logging.info(f"检测到 {AUTH_JSON_PATH} 文件已更新。正在尝试自动恢复连接...")
                        initialize_api_and_cache()
                except FileNotFoundError:
                    logging.info(f"仍在等待 {AUTH_JSON_PATH} 文件的创建...")
                except Exception as e:
                    logging.error(f"自动恢复过程中发生错误: {e}")

            elif auth_status == "ok" and api_client:
                # 执行一次轻量级的API调用来检查Token是否仍然有效
                try:
                    # get_homes_list 是一个很好的、低开销的检查方法
                    api_client.get_homes_list()
                    logging.info("认证检查成功，Token 仍然有效。")
                except Exception as e:
                    logging.error(f"认证检查失败，Token 可能已过期: {e}")
                    auth_status = "expired"
                    api_client = None
            
            last_auth_check_time = time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())

# --- API 端点 ---

@app.route('/status', methods=['GET'])
def get_status():
    return jsonify({
        "service_status": "running",
        "authentication_status": auth_status,
        "last_check_time": last_auth_check_time
    })

@app.route('/scenes', methods=['GET'])
def get_scenes():
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "服务尚未就绪，认证状态不正常。"}), 503

    # 创建一个对用户友好的、不包含ID的场景列表版本
    user_friendly_scenes = {home: list(scenes.keys()) for home, scenes in name_to_id_cache.items()}
    return jsonify(user_friendly_scenes)

@app.route('/refresh', methods=['POST'])
def refresh_scenes():
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "无法刷新，认证状态不正常。"}), 401
    
    with lock:
        if refresh_cache_logic():
            return jsonify({"status": "success", "message": "缓存已成功刷新。"})
        else:
            return jsonify({"status": "error", "message": "刷新缓存失败，请检查日志获取详细信息。"}), 500

@app.route('/run_scene', methods=['POST'])
def run_scene():
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "认证已过期，请重新运行登录脚本。"}), 401

    data = request.get_json()
    if not data or 'home_name' not in data or 'scene_name' not in data:
        return jsonify({"status": "error", "message": "无效请求，必须包含 'home_name' 和 'scene_name'。 "}), 400

    home_name = data['home_name']
    scene_name = data['scene_name']
    
    try:
        scene_id = name_to_id_cache[home_name][scene_name]
    except KeyError:
        return jsonify({"status": "error", "message": f"未找到场景：无法在家庭 '{home_name}' 中找到名为 '{scene_name}' 的场景。"}), 404

    with lock:
        if not api_client:
            return jsonify({"status": "error", "message": "API 客户端不可用。"}), 500
        try:
            ret = api_client.run_scene(scene_id)
            logging.info(f"运行场景 '{scene_name}'，返回结果: {ret}")
            return jsonify({"status": "success", "message": f"场景 '{scene_name}' 在家庭 '{home_name}' 中已成功执行。", "result": ret})
        except Exception as e:
            logging.error(f"运行场景 '{scene_name}' 失败: {e}")
            return jsonify({"status": "error", "message": f"执行场景时发生错误，原因: {e}"}), 500

# --- 主程序入口 ---

if __name__ == '__main__':
    # 执行初始化
    initialize_api_and_cache()
    
    # 启动后台轮询线程
    polling = threading.Thread(target=auth_polling_thread, daemon=True)
    polling.start()
    
    # 启动 Flask web 服务
    logging.info(f"服务已启动，监听地址 http://{SERVER_HOST}:{SERVER_PORT}")
    app.run(host=SERVER_HOST, port=SERVER_PORT, debug=False)